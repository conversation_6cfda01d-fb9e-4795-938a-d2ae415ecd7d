/* Reset och grundläggande stilar */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4285f4;
    --primary-dark: #3367d6;
    --secondary-color: #34a853;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-color: #dadce0;
    --shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
}

/* Header */
.header {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--background-color);
    border-radius: 20px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ea4335;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--secondary-color);
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main chat area */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.chat-container {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Welcome message */
.welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.welcome-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.welcome-message h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

/* Message bubbles */
.message {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: white;
}

.message.assistant .message-avatar {
    background: var(--secondary-color);
    color: white;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    position: relative;
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: var(--background-color);
    color: var(--text-primary);
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 4px;
    opacity: 0.7;
}

/* Chat input */
.chat-input-container {
    border-top: 1px solid var(--border-color);
    padding: 20px;
}

.image-preview {
    margin-bottom: 12px;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px;
    background: var(--background-color);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.remove-image {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.remove-image:hover {
    background: var(--border-color);
    color: #ea4335;
}

#previewImg {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--border-radius);
    object-fit: contain;
}

.chat-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    margin-bottom: 12px;
}

.input-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-button {
    width: 44px;
    height: 44px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--surface-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.action-button:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(66, 133, 244, 0.05);
}

.action-button.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

#messageInput {
    flex: 1;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    transition: var(--transition);
    max-height: 120px;
    min-height: 44px;
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.send-button {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.send-button:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.char-counter {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-action {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    background: var(--surface-color);
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.quick-action:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Settings panel */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--surface-color);
    box-shadow: var(--shadow-hover);
    transition: var(--transition);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.settings-panel.open {
    right: 0;
}

.settings-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--background-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    background: var(--border-color);
}

.settings-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.setting-group {
    margin-bottom: 24px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.api-key-input {
    position: relative;
}

.toggle-visibility {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
}

.setting-group small {
    display: block;
    margin-top: 4px;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.setting-group input[type="range"] {
    padding: 0;
}

/* Floating buttons */
.settings-button,
.clear-button {
    position: fixed;
    width: 56px;
    height: 56px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-hover);
    z-index: 100;
}

.settings-button {
    bottom: 100px;
    right: 30px;
}

.clear-button {
    bottom: 30px;
    right: 30px;
    background: #ea4335;
}

.settings-button:hover,
.clear-button:hover {
    transform: scale(1.1);
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: var(--surface-color);
    padding: 40px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-hover);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        gap: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .settings-panel {
        width: 100%;
        right: -100%;
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .input-footer {
        flex-direction: column;
        align-items: center;
    }
}
