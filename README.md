# Gemini AI Chat

En snygg och modern webbapplikation för att chatta med Google Gemini AI. Byggd med vanilla HTML, CSS och JavaScript för enkel deployment.

## ✨ Funktioner

- **Modern design** - Responsiv och snygg användargränssnitt
- **Google Gemini AI** - Integration med Googles senaste AI-modell
- **Säker API-hantering** - API-nycklar sparas endast lokalt i webbläsaren
- **Chat-historik** - Automatisk sparning av konversationer
- **Anpassningsbara inställningar** - Välj modell och kreativitetsnivå
- **Snabbkommandon** - Fördefinierade prompter för vanliga uppgifter
- **Responsiv design** - Fungerar på desktop, tablet och mobil

## 🚀 Kom igång

### 1. Skaffa en Google Gemini API-nyckel

1. Gå till [Google AI Studio](https://makersuite.google.com/app/apikey)
2. <PERSON>gg<PERSON> in med ditt Google-konto
3. <PERSON><PERSON><PERSON> på "Create API Key"
4. Ko<PERSON>ra din API-nyckel

### 2. Konfigurera applikationen

1. Öppna `index.html` i din webbläsare
2. Klicka på kugghjulet (⚙️) för att öppna inställningar
3. Klistra in din API-nyckel i fältet "Google Gemini API Nyckel"
4. Välj önskad modell och kreativitetsnivå
5. Stäng inställningspanelen

### 3. Börja chatta!

- Skriv ditt meddelande i textfältet
- Tryck Enter eller klicka på skicka-knappen
- Använd snabbkommandona för vanliga uppgifter

## 📁 Filstruktur

```
gemini-chat/
├── index.html          # Huvudsidan
├── styles.css          # Stilar och design
├── script.js           # JavaScript-logik
├── config.example.js   # Exempel på konfiguration
└── README.md           # Denna fil
```

## 🔧 Anpassning

### Ändra färgtema

Redigera CSS-variablerna i `styles.css`:

```css
:root {
    --primary-color: #4285f4;    /* Huvudfärg */
    --secondary-color: #34a853;  /* Sekundär färg */
    --background-color: #f8f9fa; /* Bakgrundsfärg */
    /* ... */
}
```

### Lägg till nya snabbkommandon

Redigera HTML-koden i `index.html` och lägg till nya knappar:

```html
<button class="quick-action" data-prompt="Din prompt här:">
    <i class="fas fa-icon"></i>
    Knapptext
</button>
```

## 🌐 Deployment till one.com

### Förberedelser

1. Kontrollera att alla filer fungerar lokalt
2. Testa med din API-nyckel
3. Optimera filer om nödvändigt

### Upload till one.com

1. Logga in på ditt one.com kontrollpanel
2. Gå till "Filhanterare" eller "File Manager"
3. Navigera till din webbkatalog (oftast `public_html` eller `www`)
4. Skapa en ny mapp för din app (t.ex. `gemini-chat`)
5. Ladda upp alla filer:
   - `index.html`
   - `styles.css`
   - `script.js`
   - `README.md` (valfritt)

### Åtkomst

Din app kommer att vara tillgänglig på:
`https://dindomän.com/gemini-chat/`

## 🔒 Säkerhet

- **API-nycklar**: Sparas endast lokalt i webbläsaren (localStorage)
- **HTTPS**: Använd alltid HTTPS för säker kommunikation
- **Ingen server**: Appen kör helt i webbläsaren, inga servrar behövs

## 🛠️ Felsökning

### API-fel

- Kontrollera att din API-nyckel är korrekt
- Verifiera att du har aktiverat Gemini API i Google Cloud Console
- Kontrollera nätverksanslutningen

### Designproblem

- Kontrollera att alla CSS-filer laddas korrekt
- Verifiera att Font Awesome ikoner laddas
- Testa i olika webbläsare

### JavaScript-fel

- Öppna utvecklarverktyg (F12) och kontrollera konsolen
- Verifiera att alla JavaScript-filer laddas
- Kontrollera för syntaxfel

## 📱 Webbläsarstöd

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🤝 Bidrag

Denna app är byggd för personligt bruk men du är välkommen att:
- Föreslå förbättringar
- Rapportera buggar
- Dela dina anpassningar

## 📄 Licens

Fri att använda för personligt bruk. Se till att följa Googles användarvillkor för Gemini API.

## 🔗 Användbara länkar

- [Google Gemini API Dokumentation](https://ai.google.dev/docs)
- [Google AI Studio](https://makersuite.google.com/)
- [Font Awesome Ikoner](https://fontawesome.com/icons)
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/)

---

**Njut av din personliga AI-assistent! 🤖✨**
