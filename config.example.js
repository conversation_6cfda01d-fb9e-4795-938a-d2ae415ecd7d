// Exempel på konfigurationsfil för Gemini API
// Kopiera denna fil till config.js och fyll i din API-nyckel

const CONFIG = {
    // Din Google Gemini API-nyckel
    // Få din nyckel från: https://makersuite.google.com/app/apikey
    GEMINI_API_KEY: 'DIN_API_NYCKEL_HÄR',
    
    // Standard modell att använda
    DEFAULT_MODEL: 'gemini-pro',
    
    // Standard temperatur (kreativitet) 0.0 - 1.0
    DEFAULT_TEMPERATURE: 0.7,
    
    // Maximal längd på meddelanden
    MAX_MESSAGE_LENGTH: 2000,
    
    // API endpoint
    API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta'
};

// Exportera konfigurationen (om du använder moduler)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
