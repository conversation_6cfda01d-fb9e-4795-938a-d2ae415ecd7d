# Deployment Guide för one.com

## 📋 Checklista innan deployment

- [ ] Testa appen lokalt med din Gemini API-nyckel
- [ ] Kontrollera att alla filer fungerar korrekt
- [ ] Verifiera responsiv design på olika skärmstorlekar
- [ ] Testa alla funktioner (skicka meddelanden, inställningar, etc.)

## 🚀 Steg-för-steg deployment till one.com

### Steg 1: Förbered filerna

Kontrollera att du har följande filer:
```
flash/
├── index.html
├── styles.css
├── script.js
├── README.md
└── DEPLOYMENT.md
```

### Steg 2: Logga in på one.com

1. Gå till [one.com](https://www.one.com)
2. Logga in på ditt konto
3. Gå till "Kontrollpanel" eller "Control Panel"

### Steg 3: Öppna filhanteraren

1. Klicka på "Filhanterare" eller "File Manager"
2. Navigera till din webbkatalog:
   - <PERSON><PERSON><PERSON><PERSON> `public_html/` 
   - Eller `www/`
   - Eller `htdocs/`

### Steg 4: Skapa mapp för appen

1. Skapa en ny mapp, t.ex. `gemini-chat`
2. Gå in i mappen

### Steg 5: Ladda upp filer

Ladda upp följande filer till mappen:
- `index.html` (obligatorisk)
- `styles.css` (obligatorisk)
- `script.js` (obligatorisk)
- `README.md` (valfri)

**Viktigt:** Ladda INTE upp `config.example.js` eller andra konfigurationsfiler med känslig information.

### Steg 6: Testa deployment

1. Öppna din webbläsare
2. Gå till: `https://dindomän.com/gemini-chat/`
3. Kontrollera att sidan laddas korrekt
4. Testa att lägga till din API-nyckel i inställningarna
5. Skicka ett testmeddelande

## 🔧 Felsökning

### Problem: Sidan laddas inte

**Lösningar:**
- Kontrollera att `index.html` finns i rätt mapp
- Verifiera att filnamnen är korrekta (case-sensitive)
- Kontrollera att mappen har rätt behörigheter

### Problem: CSS/JavaScript laddas inte

**Lösningar:**
- Kontrollera att alla filer är uppladdade
- Verifiera filsökvägar i `index.html`
- Kontrollera att filerna inte är korrupta

### Problem: API fungerar inte

**Lösningar:**
- Kontrollera din Gemini API-nyckel
- Verifiera att HTTPS används (one.com bör ha detta automatiskt)
- Kontrollera webbläsarens konsol för felmeddelanden

## 🌐 Alternativa deployment-metoder

### Via FTP

Om du föredrar FTP:
1. Använd en FTP-klient som FileZilla
2. Anslut med dina one.com FTP-uppgifter
3. Ladda upp filerna till rätt mapp

### Via one.com's Website Builder

Om du använder Website Builder:
1. Skapa en ny sida
2. Lägg till en "HTML/CSS/JS" widget
3. Kopiera innehållet från dina filer

## 🔒 Säkerhetsrekommendationer

### För one.com deployment

1. **Använd HTTPS**: one.com tillhandahåller automatiskt SSL
2. **Skydda känsliga filer**: Lägg aldrig upp filer med API-nycklar
3. **Regelbundna backups**: Spara lokala kopior av dina filer
4. **Övervaka användning**: Håll koll på din API-användning

### API-säkerhet

1. **Begränsa API-nyckel**: Konfigurera begränsningar i Google Cloud Console
2. **Övervaka användning**: Kontrollera regelbundet din API-användning
3. **Rotera nycklar**: Byt API-nyckel regelbundet

## 📱 Testning efter deployment

### Desktop-test
- [ ] Chrome
- [ ] Firefox  
- [ ] Safari
- [ ] Edge

### Mobil-test
- [ ] iPhone Safari
- [ ] Android Chrome
- [ ] Tablet

### Funktionstest
- [ ] Skicka meddelande
- [ ] Öppna/stänga inställningar
- [ ] Ändra modell
- [ ] Justera temperatur
- [ ] Rensa chat
- [ ] Snabbkommandon

## 🎯 Optimeringar för produktion

### Prestanda
- Filerna är redan optimerade för snabb laddning
- Använder CDN för externa bibliotek (Font Awesome, Google Fonts)
- Minimal JavaScript och CSS

### SEO (om relevant)
- Lägg till meta-beskrivning i `index.html`
- Lägg till favicon
- Konfigurera robots.txt om nödvändigt

## 📞 Support

### one.com support
- Kontakta one.com support för hosting-relaterade frågor
- Använd deras kunskapsbas för vanliga problem

### Gemini API support
- Läs [Google AI dokumentation](https://ai.google.dev/docs)
- Kontrollera [API-status](https://status.cloud.google.com/)

---

**Lycka till med din deployment! 🚀**
