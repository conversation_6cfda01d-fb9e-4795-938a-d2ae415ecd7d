<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini AI Chat</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>Gemini AI</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ansluter...</span>
                </div>
            </div>
        </header>

        <main class="main">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message">
                        <div class="welcome-icon">
                            <i class="fas fa-sparkles"></i>
                        </div>
                        <h2>Välkommen till Gemini AI</h2>
                        <p>Ställ mig vilken fråga som helst så hjälper jag dig!</p>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="messageInput" 
                            placeholder="Skriv ditt meddelande här..."
                            rows="1"
                            maxlength="2000"
                        ></textarea>
                        <button id="sendButton" class="send-button" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="input-footer">
                        <span class="char-counter">
                            <span id="charCount">0</span>/2000
                        </span>
                        <div class="quick-actions">
                            <button class="quick-action" data-prompt="Förklara detta enkelt:">
                                <i class="fas fa-lightbulb"></i>
                                Förklara
                            </button>
                            <button class="quick-action" data-prompt="Sammanfatta detta:">
                                <i class="fas fa-compress-alt"></i>
                                Sammanfatta
                            </button>
                            <button class="quick-action" data-prompt="Översätt detta till engelska:">
                                <i class="fas fa-language"></i>
                                Översätt
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div class="settings-panel" id="settingsPanel">
            <div class="settings-header">
                <h3>Inställningar</h3>
                <button id="closeSettings" class="close-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label for="apiKey">Google Gemini API Nyckel:</label>
                    <div class="api-key-input">
                        <input type="password" id="apiKey" placeholder="Ange din API-nyckel">
                        <button id="toggleApiKey" class="toggle-visibility">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small>Din API-nyckel sparas endast lokalt i din webbläsare</small>
                </div>
                <div class="setting-group">
                    <label for="modelSelect">Modell:</label>
                    <select id="modelSelect">
                        <option value="gemini-pro">Gemini Pro</option>
                        <option value="gemini-pro-vision">Gemini Pro Vision</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="temperature">Kreativitet (Temperature):</label>
                    <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                    <span id="temperatureValue">0.7</span>
                </div>
            </div>
        </div>

        <button id="settingsButton" class="settings-button">
            <i class="fas fa-cog"></i>
        </button>

        <button id="clearChat" class="clear-button">
            <i class="fas fa-trash"></i>
        </button>
    </div>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Gemini tänker...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
