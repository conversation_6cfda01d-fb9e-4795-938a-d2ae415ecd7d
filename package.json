{"name": "gemini-flash-chat", "version": "1.0.0", "description": "Gemini 2.5 Flash Image Preview Chat Application", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js"}, "dependencies": {"@google/generative-ai": "^0.21.0", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1"}, "keywords": ["gemini", "ai", "chat", "image-generation", "google-ai"], "author": "<PERSON>", "license": "MIT"}