# Gemini 2.5 Flash Chat - Lokal Installation

En modern webbapplikation för att chatta med Google Gemini 2.5 Flash Image Preview som kan både analysera och generera bilder.

## 🚀 Snabbstart

### 1. Installera Node.js
Kontrollera att du har Node.js installerat (version 18 eller senare):
```bash
node --version
npm --version
```

Om du inte har Node.js, ladda ner från [nodejs.org](https://nodejs.org/)

### 2. Installera dependencies
```bash
npm install
```

### 3. Konfigurera API-nyckel
Din API-nyckel är redan konfigurerad i `.env`-filen.

### 4. Starta servern
```bash
npm start
```

Eller för utveckling med auto-restart:
```bash
npm run dev
```

### 5. Öppna webbläsaren
Gå till: http://localhost:3000

## ✨ Funktioner

### 🎨 Bildgenerering
- Skriv "Skapa en bild av..." för att generera bilder
- <PERSON><PERSON><PERSON> på "Skapa bild"-knappen för snabbstart
- Genererade bilder visas med grön ram

### 📸 Bildanalys
- Klicka på kamera-ikonen för att ladda upp bilder
- Ställ frågor om bildinnehåll
- Stöder alla vanliga bildformat

### 💬 Textchat
- Vanlig textkonversation med Gemini
- Snabbkommandon för vanliga uppgifter
- Chat-historik sparas automatiskt

## 🔧 API Endpoints

### GET /health
Kontrollera serverstatus och API-konfiguration

### POST /api/chat
Skicka textmeddelanden till Gemini
```json
{
  "message": "Hej Gemini!",
  "model": "gemini-2.5-flash-image-preview",
  "temperature": 0.7
}
```

### POST /api/chat-with-image
Skicka meddelanden med bilder (multipart/form-data)
- `message`: Textmeddelande (valfritt)
- `image`: Bildfil
- `model`: Gemini-modell
- `temperature`: Kreativitetsnivå

## 🛠️ Utveckling

### Projektstruktur
```
flash/
├── server.js           # Express server med Gemini API
├── index.html          # Frontend HTML
├── styles.css          # CSS stilar
├── script.js           # Frontend JavaScript
├── package.json        # Node.js dependencies
├── .env               # API-nyckel (ej i git)
├── .env.example       # Exempel på miljövariabler
└── README-LOCAL.md    # Denna fil
```

### Miljövariabler
```bash
GEMINI_API_KEY=din_api_nyckel
PORT=3000
NODE_ENV=development
```

### Loggar
Servern loggar alla requests och fel till konsolen.

## 🔒 Säkerhet

- API-nyckeln lagras säkert på servern
- Ingen känslig information skickas till frontend
- CORS konfigurerat för lokal utveckling
- Filuppladdning begränsad till bilder (max 10MB)

## 🐛 Felsökning

### Server startar inte
1. Kontrollera att Node.js är installerat
2. Kör `npm install` igen
3. Kontrollera att port 3000 är ledig

### API-fel
1. Kontrollera att `.env`-filen finns
2. Verifiera API-nyckeln på [Google AI Studio](https://makersuite.google.com/app/apikey)
3. Kontrollera serverlogs i terminalen

### Bilduppladdning fungerar inte
1. Kontrollera filstorlek (max 10MB)
2. Använd endast bildformat (JPG, PNG, GIF, etc.)
3. Kontrollera nätverksanslutning

## 📱 Deployment till Raspberry Pi 5

När du kommer hem kan du enkelt flytta detta till din Pi5:

1. **Kopiera filerna** till Pi5
2. **Installera Node.js** på Pi5
3. **Kör `npm install`** på Pi5
4. **Starta servern** med `npm start`
5. **Åtkomst från nätverket** via Pi5:s IP-adress

### Pi5 Optimering
```bash
# För Pi5, använd PM2 för process management
npm install -g pm2
pm2 start server.js --name gemini-chat
pm2 startup
pm2 save
```

## 🔗 Användbara kommandon

```bash
# Starta server
npm start

# Utvecklingsläge (auto-restart)
npm run dev

# Kontrollera serverstatus
curl http://localhost:3000/health

# Testa API direkt
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hej från terminalen!"}'
```

## 📊 Prestanda

- **Snabb start**: ~2 sekunder
- **Minneskrav**: ~50MB RAM
- **CPU**: Minimal användning i vila
- **Nätverkstrafik**: Endast till Google API

---

**Njut av din lokala Gemini AI-assistent! 🤖✨**
