class GeminiChat {
    constructor() {
        this.model = localStorage.getItem('gemini-model') || 'gemini-2.5-flash-image-preview';
        this.temperature = parseFloat(localStorage.getItem('gemini-temperature')) || 0.7;
        this.chatHistory = JSON.parse(localStorage.getItem('chat-history')) || [];
        this.selectedImage = null;
        this.serverUrl = window.location.origin; // Use same origin as the page

        this.initializeElements();
        this.bindEvents();
        this.loadChatHistory();
        this.checkServerStatus();
        this.autoResizeTextarea();
    }

    initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            settingsPanel: document.getElementById('settingsPanel'),
            settingsButton: document.getElementById('settingsButton'),
            closeSettings: document.getElementById('closeSettings'),
            clearChat: document.getElementById('clearChat'),
            apiKeyInput: document.getElementById('apiKey'),
            toggleApiKey: document.getElementById('toggleApiKey'),
            modelSelect: document.getElementById('modelSelect'),
            temperatureSlider: document.getElementById('temperature'),
            temperatureValue: document.getElementById('temperatureValue'),
            statusDot: document.getElementById('statusDot'),
            statusText: document.getElementById('statusText'),
            charCount: document.getElementById('charCount'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            imageButton: document.getElementById('imageButton'),
            imageInput: document.getElementById('imageInput'),
            imagePreview: document.getElementById('imagePreview'),
            previewImg: document.getElementById('previewImg'),
            removeImage: document.getElementById('removeImage')
        };

        // Set initial values
        this.elements.modelSelect.value = this.model;
        this.elements.temperatureSlider.value = this.temperature;
        this.elements.temperatureValue.textContent = this.temperature;

        // Hide API key input since we're using server
        const apiKeyGroup = this.elements.apiKeyInput.closest('.setting-group');
        if (apiKeyGroup) {
            apiKeyGroup.style.display = 'none';
        }
    }

    bindEvents() {
        // Send message events
        this.elements.sendButton.addEventListener('click', () => this.sendMessage());
        this.elements.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Input events
        this.elements.messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.updateSendButton();
            this.autoResizeTextarea();
        });

        // Settings events
        this.elements.settingsButton.addEventListener('click', () => this.toggleSettings());
        this.elements.closeSettings.addEventListener('click', () => this.toggleSettings());
        
        // API key events removed since we're using server
        this.elements.modelSelect.addEventListener('change', () => this.saveModel());
        this.elements.temperatureSlider.addEventListener('input', () => this.updateTemperature());

        // Image upload events
        this.elements.imageButton.addEventListener('click', () => this.elements.imageInput.click());
        this.elements.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
        this.elements.removeImage.addEventListener('click', () => this.removeSelectedImage());

        // Other events
        this.elements.clearChat.addEventListener('click', () => this.clearChat());

        // Quick actions
        document.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', () => {
                const prompt = button.dataset.prompt;
                this.elements.messageInput.value = prompt + ' ';
                this.elements.messageInput.focus();
                this.updateCharCount();
                this.updateSendButton();
            });
        });

        // Close settings when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.elements.settingsPanel.contains(e.target) && 
                !this.elements.settingsButton.contains(e.target) &&
                this.elements.settingsPanel.classList.contains('open')) {
                this.toggleSettings();
            }
        });
    }

    updateCharCount() {
        const count = this.elements.messageInput.value.length;
        this.elements.charCount.textContent = count;
        
        if (count > 1800) {
            this.elements.charCount.style.color = '#ea4335';
        } else if (count > 1500) {
            this.elements.charCount.style.color = '#fbbc04';
        } else {
            this.elements.charCount.style.color = '#5f6368';
        }
    }

    updateSendButton() {
        const hasText = this.elements.messageInput.value.trim().length > 0;
        const hasImage = this.selectedImage !== null;
        this.elements.sendButton.disabled = !hasText && !hasImage;
    }

    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    async checkServerStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/health`);
            const data = await response.json();

            if (data.status === 'OK' && data.geminiConfigured) {
                this.elements.statusDot.classList.add('connected');
                this.elements.statusText.textContent = 'Server ansluten';
            } else {
                this.elements.statusDot.classList.remove('connected');
                this.elements.statusText.textContent = 'Server ej konfigurerad';
            }
        } catch (error) {
            this.elements.statusDot.classList.remove('connected');
            this.elements.statusText.textContent = 'Server offline';
            console.error('Server status check failed:', error);
        }
        this.updateSendButton();
    }

    toggleSettings() {
        this.elements.settingsPanel.classList.toggle('open');
    }

    // API key functions removed since we're using server

    saveModel() {
        this.model = this.elements.modelSelect.value;
        localStorage.setItem('gemini-model', this.model);
    }

    updateTemperature() {
        this.temperature = parseFloat(this.elements.temperatureSlider.value);
        this.elements.temperatureValue.textContent = this.temperature;
        localStorage.setItem('gemini-temperature', this.temperature.toString());
    }

    handleImageSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('Vänligen välj en bildfil.');
            return;
        }

        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Bilden är för stor. Maximal storlek är 10MB.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.selectedImage = {
                data: e.target.result.split(',')[1], // Remove data:image/...;base64, prefix
                mimeType: file.type
            };

            this.elements.previewImg.src = e.target.result;
            this.elements.imagePreview.style.display = 'block';
            this.elements.imageButton.classList.add('active');
            this.updateSendButton();
        };

        reader.readAsDataURL(file);
    }

    removeSelectedImage() {
        this.selectedImage = null;
        this.elements.imagePreview.style.display = 'none';
        this.elements.imageButton.classList.remove('active');
        this.elements.imageInput.value = '';
        this.updateSendButton();
    }

    async sendMessage() {
        const message = this.elements.messageInput.value.trim();
        const hasImage = this.selectedImage !== null;

        if ((!message && !hasImage) || !this.apiKey) return;

        // Add user message (with image if present)
        this.addMessage('user', message, this.selectedImage);

        // Clear input and image
        this.elements.messageInput.value = '';
        this.removeSelectedImage();
        this.updateCharCount();
        this.updateSendButton();
        this.autoResizeTextarea();

        // Show loading
        this.showLoading(true);

        try {
            const response = await this.callGeminiAPI(message, hasImage ? this.selectedImage : null);

            // Handle both old string format and new object format
            if (typeof response === 'string') {
                this.addMessage('assistant', response);
            } else {
                // New format with text and images
                this.addMessage('assistant', response.text, null, response.images);
            }
        } catch (error) {
            console.error('Error calling Gemini API:', error);
            this.addMessage('assistant', 'Ursäkta, det uppstod ett fel när jag försökte svara. Kontrollera din API-nyckel och försök igen.');
        } finally {
            this.showLoading(false);
        }
    }

    async callGeminiAPI(message, image = null) {
        if (image) {
            // Use multipart form data for image uploads
            const formData = new FormData();
            formData.append('message', message || '');
            formData.append('model', this.model);
            formData.append('temperature', this.temperature.toString());

            // Convert base64 image to blob
            const imageBlob = this.base64ToBlob(image.data, image.mimeType);
            formData.append('image', imageBlob, 'image.' + image.mimeType.split('/')[1]);

            const response = await fetch(`${this.serverUrl}/api/chat-with-image`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'API request failed');
            }

            return await response.json();
        } else {
            // Text-only request
            const response = await fetch(`${this.serverUrl}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    model: this.model,
                    temperature: this.temperature
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'API request failed');
            }

            return await response.json();
        }
    }

    base64ToBlob(base64, mimeType) {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    addMessage(sender, content, image = null, generatedImages = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const time = new Date().toLocaleTimeString('sv-SE', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let imageHtml = '';

        // User uploaded image
        if (image && sender === 'user') {
            imageHtml = `<div class="message-image">
                <img src="data:${image.mimeType};base64,${image.data}" alt="Uploaded image" style="max-width: 300px; max-height: 200px; border-radius: 8px; margin-bottom: 8px;">
            </div>`;
        }

        // AI generated images
        if (generatedImages && generatedImages.length > 0 && sender === 'assistant') {
            imageHtml = generatedImages.map(img =>
                `<div class="message-image generated-image">
                    <img src="data:${img.mimeType};base64,${img.data}" alt="Generated image" style="max-width: 400px; max-height: 300px; border-radius: 8px; margin: 8px 0; cursor: pointer;" onclick="this.style.maxWidth = this.style.maxWidth === '100%' ? '400px' : '100%'">
                    <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 4px;">
                        <i class="fas fa-magic"></i> Genererad av Gemini
                    </div>
                </div>`
            ).join('');
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas ${sender === 'user' ? 'fa-user' : 'fa-robot'}"></i>
            </div>
            <div class="message-content">
                ${imageHtml}
                ${content ? this.formatMessage(content) : ''}
                <div class="message-time">${time}</div>
            </div>
        `;

        // Remove welcome message if it exists
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.elements.chatMessages.appendChild(messageDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;

        // Save to history
        this.chatHistory.push({ sender, content, time });
        localStorage.setItem('chat-history', JSON.stringify(this.chatHistory));
    }

    formatMessage(content) {
        // Simple formatting for code blocks and line breaks
        return content
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    loadChatHistory() {
        if (this.chatHistory.length === 0) return;

        // Remove welcome message
        const welcomeMessage = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.chatHistory.forEach(({ sender, content, time }) => {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas ${sender === 'user' ? 'fa-user' : 'fa-robot'}"></i>
                </div>
                <div class="message-content">
                    ${this.formatMessage(content)}
                    <div class="message-time">${time}</div>
                </div>
            `;

            this.elements.chatMessages.appendChild(messageDiv);
        });

        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    clearChat() {
        if (confirm('Är du säker på att du vill rensa chatten?')) {
            this.chatHistory = [];
            localStorage.removeItem('chat-history');
            
            this.elements.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-sparkles"></i>
                    </div>
                    <h2>Välkommen till Gemini AI</h2>
                    <p>Ställ mig vilken fråga som helst så hjälper jag dig!</p>
                </div>
            `;
        }
    }

    showLoading(show) {
        if (show) {
            this.elements.loadingOverlay.classList.add('show');
        } else {
            this.elements.loadingOverlay.classList.remove('show');
        }
    }
}

// Initialize the chat when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new GeminiChat();
});
