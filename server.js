import express from 'express';
import cors from 'cors';
import multer from 'multer';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { GoogleGenerativeAI } from '@google/generative-ai';

// ES modules setup
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configure multer for file uploads
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Only image files are allowed'), false);
        }
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        geminiConfigured: !!process.env.GEMINI_API_KEY
    });
});

// Chat endpoint
app.post('/api/chat', async (req, res) => {
    try {
        const { message, model = 'gemini-2.5-flash-image-preview', temperature = 0.7 } = req.body;

        if (!message) {
            return res.status(400).json({ error: 'Message is required' });
        }

        if (!process.env.GEMINI_API_KEY) {
            return res.status(500).json({ error: 'Gemini API key not configured' });
        }

        // Get the model
        const geminiModel = genAI.getGenerativeModel({
            model: model,
            generationConfig: {
                temperature: temperature,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1024
            }
        });

        // Generate content
        const result = await geminiModel.generateContent(message);
        const response = await result.response;
        const text = response.text();

        // For now, only return text (image generation needs newer API)
        let responseText = text;
        let responseImages = [];

        res.json({
            text: responseText || 'Gemini genererade en bild utan text.',
            images: responseImages,
            model: model,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Chat error:', error);
        res.status(500).json({ 
            error: 'Failed to generate response',
            details: error.message 
        });
    }
});

// Chat with image endpoint
app.post('/api/chat-with-image', upload.single('image'), async (req, res) => {
    try {
        const { message, model = 'gemini-2.5-flash-image-preview', temperature = 0.7 } = req.body;
        const imageFile = req.file;

        if (!message && !imageFile) {
            return res.status(400).json({ error: 'Message or image is required' });
        }

        if (!process.env.GEMINI_API_KEY) {
            return res.status(500).json({ error: 'Gemini API key not configured' });
        }

        // Get the model (use vision model for images)
        const modelName = imageFile ? 'gemini-pro-vision' : model;
        const geminiModel = genAI.getGenerativeModel({
            model: modelName,
            generationConfig: {
                temperature: temperature,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1024
            }
        });

        // Prepare content parts
        const parts = [];

        if (message) {
            parts.push({ text: message });
        }

        if (imageFile) {
            parts.push({
                inlineData: {
                    mimeType: imageFile.mimetype,
                    data: imageFile.buffer.toString('base64')
                }
            });
        }

        // Generate content
        const result = await geminiModel.generateContent(parts);
        const response = await result.response;
        const text = response.text();

        // For now, only return text
        let responseText = text;
        let responseImages = [];

        res.json({
            text: responseText || 'Gemini analyserade bilden.',
            images: responseImages,
            model: model,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Chat with image error:', error);
        res.status(500).json({ 
            error: 'Failed to generate response',
            details: error.message 
        });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
        }
    }
    
    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Gemini Flash Chat server running on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    
    if (!process.env.GEMINI_API_KEY) {
        console.warn('⚠️  GEMINI_API_KEY not set. Please create a .env file with your API key.');
    } else {
        console.log('✅ Gemini API key configured');
    }
});
